<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              label: '回复',
              icon: 'ant-design:message-outlined',
              onClick: handleReply.bind(null, record),
            },
            {
              label: '删除',
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '确定要删除这条留言吗？',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { FormSchema } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '发送者',
      dataIndex: 'sender',
      width: 120,
      resizable: true,
    },
    {
      title: '关联产品信息',
      dataIndex: 'productInfo',
      width: 200,
      resizable: true,
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      width: 300,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '消息来源',
      dataIndex: 'source',
      width: 120,
      resizable: true,
    },
    {
      title: '发送时间',
      dataIndex: 'sendTime',
      width: 180,
      resizable: true,
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'source',
      label: '消息来源',
      component: 'Select',
      componentProps: {
        placeholder: '请选择消息来源',
        options: [
          { label: '网站留言', value: 'website' },
          { label: '微信客服', value: 'wechat' },
          { label: '电话咨询', value: 'phone' },
          { label: '邮件咨询', value: 'email' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'status',
      label: '消息状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择消息状态',
        options: [
          { label: '未读', value: 'unread' },
          { label: '已读', value: 'read' },
          { label: '已回复', value: 'replied' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'timeRange',
      label: '时间区间',
      component: 'RangePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
      },
      colProps: { span: 8 },
    },
  ];

  // 模拟数据
  const mockData = [
    {
      id: '1',
      sender: '张三',
      productInfo: '智能手机 iPhone 15',
      content: '请问这款手机有什么颜色可以选择？价格如何？',
      source: '网站留言',
      sendTime: '2024-01-15 10:30:00',
      status: 'unread',
    },
    {
      id: '2',
      sender: '李四',
      productInfo: '笔记本电脑 MacBook Pro',
      content: '这款电脑的配置怎么样？适合做设计工作吗？',
      source: '微信客服',
      sendTime: '2024-01-15 14:20:00',
      status: 'read',
    },
    {
      id: '3',
      sender: '王五',
      productInfo: '无线耳机 AirPods Pro',
      content: '耳机的降噪效果如何？电池续航时间多长？',
      source: '电话咨询',
      sendTime: '2024-01-16 09:15:00',
      status: 'replied',
    },
    {
      id: '4',
      sender: '赵六',
      productInfo: '智能手表 Apple Watch',
      content: '手表支持哪些运动模式？防水等级是多少？',
      source: '邮件咨询',
      sendTime: '2024-01-16 16:45:00',
      status: 'unread',
    },
    {
      id: '5',
      sender: '钱七',
      productInfo: '平板电脑 iPad Air',
      content: '平板的屏幕尺寸多大？支持手写笔吗？',
      source: '网站留言',
      sendTime: '2024-01-17 11:30:00',
      status: 'read',
    },
  ];

  // 模拟API请求
  const fetchData = async (params: any) => {
    console.log('查询参数:', params);
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    return {
      items: mockData,
      total: mockData.length,
    };
  };

  // 表格配置
  const [registerTable, { reload }] = useTable({
    api: fetchData,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    inset: true,
    maxHeight: 478,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    rowKey: 'id',
    formConfig: {
      labelWidth: 64,
      size: 'large',
      schemas: searchFormSchema,
    },
  });

  // 回复留言
  function handleReply(record: any) {
    console.log('回复留言:', record);
    createMessage.info(`回复给 ${record.sender} 的留言功能待开发`);
  }

  // 删除留言
  function handleDelete(record: any) {
    console.log('删除留言:', record);
    createMessage.success(`已删除 ${record.sender} 的留言`);
    reload();
  }
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
    :deep(.ant-pagination) {
      margin-bottom: -24px !important;
    }
    :deep(.ant-form) {
      padding: 0;
    }
  }

  :deep(.ant-form-item-control-input-content) {
    button {
      margin-right: 0;
      margin-left: 8px;
      box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
    }
  }
</style>
