<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <template #messageDetail="{ record }">
        <a-tooltip :title="record.messageDetail">
          <div class="message-detail-cell">
            {{ record.messageDetail }}
          </div>
        </a-tooltip>
      </template>
      <template #messageStatus="{ record }">
        <a-tag :color="getStatusColor(record.messageStatus)">
          {{ getStatusText(record.messageStatus) }}
        </a-tag>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { FormSchema } from '/@/components/Form';
  import { Tag as ATag, Tooltip as ATooltip } from 'ant-design-vue';

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '消息详情',
      dataIndex: 'messageDetail',
      width: 400,
      resizable: true,
      slots: { customRender: 'messageDetail' },
    },
    {
      title: '消息标题',
      dataIndex: 'messageTitle',
      width: 200,
      resizable: true,
    },
    {
      title: '消息来源',
      dataIndex: 'source',
      width: 120,
      resizable: true,
    },
    {
      title: '消息状态',
      dataIndex: 'messageStatus',
      width: 120,
      resizable: true,
      slots: { customRender: 'messageStatus' },
    },
    {
      title: '发送时间',
      dataIndex: 'sendTime',
      width: 180,
      resizable: true,
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'source',
      label: '消息来源',
      component: 'Select',
      componentProps: {
        placeholder: '请选择消息来源',
        options: [
          { label: '系统通知', value: 'system' },
          { label: '安全提醒', value: 'security' },
          { label: '更新公告', value: 'update' },
          { label: '活动通知', value: 'activity' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'messageStatus',
      label: '消息状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择消息状态',
        options: [
          { label: '未读', value: 'unread' },
          { label: '已读', value: 'read' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'timeRange',
      label: '时间区间',
      component: 'RangePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
      },
      colProps: { span: 8 },
    },
  ];

  // 模拟数据
  const mockData = [
    {
      id: '1',
      messageDetail: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用，请提前做好相关准备工作。',
      messageTitle: '系统维护通知',
      source: '系统通知',
      messageStatus: 'unread',
      sendTime: '2024-01-15 10:30:00',
    },
    {
      id: '2',
      messageDetail: '检测到您的账户在异地登录，如非本人操作，请立即修改密码并联系客服。',
      messageTitle: '安全登录提醒',
      source: '安全提醒',
      messageStatus: 'read',
      sendTime: '2024-01-15 14:20:00',
    },
    {
      id: '3',
      messageDetail: '新版本v2.1.0已发布，新增了多项实用功能，包括消息中心、数据导出等，欢迎体验。',
      messageTitle: '版本更新公告',
      source: '更新公告',
      messageStatus: 'read',
      sendTime: '2024-01-16 09:15:00',
    },
    {
      id: '4',
      messageDetail: '春节特惠活动开始啦！全场商品8折优惠，活动时间：1月20日-2月20日，数量有限，先到先得。',
      messageTitle: '春节特惠活动',
      source: '活动通知',
      messageStatus: 'unread',
      sendTime: '2024-01-16 16:45:00',
    },
    {
      id: '5',
      messageDetail: '您的VIP会员即将到期，到期时间：2024年2月1日，请及时续费以继续享受会员权益。',
      messageTitle: 'VIP会员到期提醒',
      source: '系统通知',
      messageStatus: 'unread',
      sendTime: '2024-01-17 11:30:00',
    },
    {
      id: '6',
      messageDetail: '数据备份已完成，备份时间：2024-01-17 02:00:00，备份文件已保存至云端存储。',
      messageTitle: '数据备份完成',
      source: '系统通知',
      messageStatus: 'read',
      sendTime: '2024-01-17 08:00:00',
    },
  ];

  // 获取状态颜色
  function getStatusColor(status: string) {
    const colorMap = {
      unread: 'red',
      read: 'green',
    };
    return colorMap[status] || 'default';
  }

  // 获取状态文本
  function getStatusText(status: string) {
    const textMap = {
      unread: '未读',
      read: '已读',
    };
    return textMap[status] || status;
  }

  // 模拟API请求
  const fetchData = async (params: any) => {
    console.log('查询参数:', params);
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    return {
      items: mockData,
      total: mockData.length,
    };
  };

  // 表格配置
  const [registerTable] = useTable({
    api: fetchData,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    inset: true,
    maxHeight: 478,
    rowKey: 'id',
    formConfig: {
      labelWidth: 64,
      size: 'large',
      schemas: searchFormSchema,
    },
  });
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
    :deep(.ant-pagination) {
      margin-bottom: -24px !important;
    }
    :deep(.ant-form) {
      padding: 0;
    }
  }

  :deep(.ant-form-item-control-input-content) {
    button {
      margin-right: 0;
      margin-left: 8px;
      box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
    }
  }

  .message-detail-cell {
    max-width: 380px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
