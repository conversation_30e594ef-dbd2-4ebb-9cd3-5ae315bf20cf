<template>
  <div class="p-4">
    <BasicTable @register="registerTable" @navigation-change="handleNavigationChange">
      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownActions(record)" />
      </template>

      <!-- 审核状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.hgyAssetEntrust?.status)">
          {{ getStatusText(record.hgyAssetEntrust?.status) }}
        </a-tag>
      </template>

      <!-- 金额格式化 -->
      <template #amount="{ text }">
        <span v-if="text">{{ formatAmount(text) }}</span>
        <span v-else>-</span>
      </template>

      <!-- 溢价率格式化 -->
      <template #premiumRate="{ text }">
        <span v-if="text !== null && text !== undefined">{{ text }}%</span>
        <span v-else>-</span>
      </template>
    </BasicTable>

    <!-- 详情查看弹窗 -->
    <DetailViewModal v-model:open="detailVisible" :record="currentRecord" :entrust-type="1" :service-type="2" @close="handleDetailClose" />
  </div>
</template>

<script lang="ts" setup name="EntrustDispose">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { ActionItem, BasicColumn } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { queryPageAll, EntrustDisposeRecord, getExportUrl, customEntrustDelete } from '/@/api/orderManage/entrustDispose';
  import { DetailViewModal } from '/@/components/Audit';
  import type { AuditRecord } from '/@/components/Audit/types';
  import { useMethods } from '/@/hooks/system/useMethods';

  const { createMessage } = useMessage();

  const { handleExportXls } = useMethods();

  const router = useRouter();

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '委托单号',
      dataIndex: 'entrustOrderId',
      width: 160,
      customRender: ({ record }) => record.hgyAssetEntrust.entrustOrderId || '-',
    },
    {
      title: '资产名称',
      dataIndex: 'assetName',
      width: 200,
      ellipsis: true,
      customRender: ({ record }) => record.hgyAssetEntrust.assetName || '-',
    },
    {
      title: '资产编号',
      dataIndex: 'assetNo',
      width: 180,
      customRender: ({ record }) => record.hgyAssetEntrust.assetNo || '-',
    },
    {
      title: '委托单位',
      dataIndex: 'entrustCompanyName',
      width: 150,
      ellipsis: true,
      customRender: ({ record }) => record.hgyEntrustOrder.entrustCompanyName || '-',
    },
    {
      title: '受委托单位',
      dataIndex: 'onEntrustCompanyName',
      width: 150,
      ellipsis: true,
      customRender: ({ record }) => record.hgyEntrustOrder.onEntrustCompanyName || '-',
    },
    {
      title: '资产数量',
      dataIndex: 'quantity',
      width: 100,
      customRender: ({ record }) => record.hgyAssetEntrust.quantity || '-',
    },
    {
      title: '计量单位',
      dataIndex: 'unit',
      width: 80,
      customRender: ({ record }) => record.hgyAssetEntrust.unit || '-',
    },
    {
      title: '资产所在地',
      dataIndex: 'address',
      width: 200,
      ellipsis: true,
      customRender: ({ record }) => record.hgyAssetEntrust.address || '-',
    },
    {
      title: '委托时间',
      dataIndex: 'createTime',
      width: 150,
      customRender: ({ record }) => {
        return record.hgyAssetEntrust.createTime ? formatToDateTime(record.hgyAssetEntrust.createTime) : '-';
      },
    },
    {
      title: '保留价',
      dataIndex: 'disposalPrice',
      width: 120,
      customRender: ({ record }) => record.hgyAssetEntrust.disposalPrice || '-',
    },
    {
      title: '成交价',
      dataIndex: 'transactionPrice',
      width: 120,
      customRender: ({ record }) => record.hgyAssetEntrust.transactionPrice || '-',
    },
    {
      title: '实际数量',
      dataIndex: 'actualQuantity',
      width: 100,
      customRender: ({ record }) => record.hgyAssetEntrust.actualQuantity || '-',
    },
    {
      title: '溢价额',
      dataIndex: 'premiumAmount',
      width: 120,
      customRender: ({ record }) => record.hgyAssetEntrust.premiumAmount || '-',
    },
    {
      title: '溢价率',
      dataIndex: 'premiumRate',
      width: 80,
      customRender: ({ record }) => record.hgyAssetEntrust.premiumRate || '-',
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      width: 100,
      slots: { customRender: 'status' },
    },
  ];

  // 导航栏配置
  const navigationItems = [
    { key: 'all', label: '全部委托', icon: '' },
    { key: 'draft', label: '未审核', icon: '' },
    { key: 'pending', label: '审核中', icon: '' },
    { key: 'approved', label: '已通过', icon: '' },
    { key: 'rejected', label: '未通过', icon: '' },
  ];

  const activeNavigationKey = ref<string | number>('all');

  // 存储当前导航的查询参数
  const currentNavParams = ref<any>({});

  // 详情查看弹窗状态
  const detailVisible = ref(false);
  const currentRecord = ref<AuditRecord | null>(null);

  // 处理导航切换
  function handleNavigationChange(key: string | number) {
    activeNavigationKey.value = key;

    // 根据导航key设置不同的查询参数
    let searchParams = {};
    switch (key) {
      case 'draft':
        searchParams = {
          status: 1, // 草稿
        };
        break;
      case 'pending':
        searchParams = {
          status: 2, // 待审核
        };
        break;
      case 'approved':
        searchParams = {
          status: 3, // 审核通过
        };
        break;
      case 'rejected':
        searchParams = {
          status: 4, // 审核拒绝
        };
        break;
      default:
        searchParams = {}; // 全部委托，不设置过滤条件
    }

    // 存储导航参数
    currentNavParams.value = searchParams;
    // 重新加载数据
    reload();
  }

  // 自定义API调用函数，可以在这里添加额外的参数处理逻辑
  async function customQueryPageAll(params: any) {
    // 可以在这里添加默认参数或者参数转换逻辑
    const defaultParams = {
      entrustType: 1, // 默认增值类型
      serviceType: 2, // 默认处置委托
    };

    // 合并导航参数和搜索表单参数
    const mergedParams = {
      ...params, // 搜索表单参数
      ...defaultParams,
      ...currentNavParams.value, // 添加导航参数
    };

    return queryPageAll(mergedParams);
  }

  // 处理导出按钮点击事件
  async function handleExport() {
    try {
      // 获取当前的搜索参数
      let searchParams = {};
      try {
        searchParams = await getForm().validate();
      } catch (error) {
        // 表单验证失败时，使用空参数
        console.warn('表单验证失败，使用空参数导出:', error);
      }

      // 合并导航参数和搜索参数
      const exportParams = {
        ...currentNavParams.value,
        ...searchParams,
      };

      // 处理时间区间参数
      if (exportParams.entrustTimeRange && Array.isArray(exportParams.entrustTimeRange) && exportParams.entrustTimeRange.length === 2) {
        exportParams.entrustTimeStart = exportParams.entrustTimeRange[0];
        exportParams.entrustTimeEnd = exportParams.entrustTimeRange[1];
        delete exportParams.entrustTimeRange;
      }

      if (exportParams.biddingTimeRange && Array.isArray(exportParams.biddingTimeRange) && exportParams.biddingTimeRange.length === 2) {
        exportParams.biddingTimeStart = exportParams.biddingTimeRange[0];
        exportParams.biddingTimeEnd = exportParams.biddingTimeRange[1];
        delete exportParams.biddingTimeRange;
      }

      console.log('导出参数:', exportParams);

      // 使用 JeecgBoot 的导出方法
      await handleExportXls('委托竞价列表', getExportUrl, exportParams);
      createMessage.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      createMessage.error('导出失败');
    }
  }

  // 表格配置
  const [registerTable, { reload, getForm }] = useTable({
    api: customQueryPageAll,
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    // 导航栏配置
    showNavigation: true,
    navigationItems,
    activeNavigationKey: activeNavigationKey.value,
    showExportButton: true,
    inset: true,
    maxHeight: 478,
    actionColumn: {
      width: 220,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    formConfig: {
      labelWidth: 64,
      // 设置表单整体大小，可选值："default" | "small" | "large" | undefined”
      size: 'large',
      schemas: [
        {
          field: 'assetName',
          label: '资产名称',
          component: 'Input',
          colProps: { span: 6 },
        },
      ],
    },
  });

  // 操作按钮配置
  function getTableAction(record: EntrustDisposeRecord): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        // ifShow: () => canEdit(record),
      },
      {
        label: '查看详情',
        onClick: handleViewDetail.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确认删除该委托吗？',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }

  // 下拉操作按钮配置
  function getDropDownActions(record: EntrustDisposeRecord): ActionItem[] {
    return [
      {
        label: '撤拍',
        popConfirm: {
          title: '确认撤拍该委托吗？',
          confirm: handleWithdraw.bind(null, record),
        },
        ifShow: false,
      },
      {
        label: '报名管理',
        onClick: handleRegistrationManage.bind(null, record),
        ifShow: false,
      },
      {
        label: '数企详情',
        onClick: handleCompanyDetail.bind(null, record),
        ifShow: false,
      },
      {
        label: '竞价记录',
        onClick: handleBiddingRecord.bind(null, record),
        ifShow: false,
      },
      {
        label: '结算信息',
        onClick: handleSettlementInfo.bind(null, record),
        ifShow: false,
      },
      {
        label: '成交确认书',
        onClick: handleDealConfirmation.bind(null, record),
        ifShow: false,
      },
      {
        label: '竞买人列表',
        onClick: handleBidderList.bind(null, record),
        ifShow: false,
      },
      {
        label: '工作报告书',
        onClick: handleWorkReport.bind(null, record),
        ifShow: false,
      },
    ];
  }

  // 权限判断函数
  function canEdit(record: EntrustDisposeRecord) {
    // 只有草稿、待审核或审核拒绝的记录可以编辑
    return [1, 2, 4].includes(record.hgyAssetEntrust.status);
  }

  function canWithdraw(record: EntrustDisposeRecord) {
    // 只有已发布的记录可以撤拍
    return record.hgyAssetEntrust.status === 5;
  }

  function canDelete(record: EntrustDisposeRecord) {
    // 只有草稿状态的记录可以删除
    return record.hgyAssetEntrust.status === 1;
  }

  function canManageRegistration(record: EntrustDisposeRecord) {
    // 只有已发布的记录可以管理报名
    return record.hgyAssetEntrust.status === 5;
  }

  function canViewSettlement(record: EntrustDisposeRecord) {
    // 只有已成交的记录可以查看结算信息
    return record.hgyAssetEntrust.status === 6;
  }

  function canViewDealConfirmation(record: EntrustDisposeRecord) {
    // 只有已成交的记录可以查看成交确认书
    return record.hgyAssetEntrust.status === 6;
  }

  function canViewWorkReport(record: EntrustDisposeRecord) {
    // 只有已成交的记录可以查看工作报告书
    return record.hgyAssetEntrust.status === 6;
  }

  // 事件处理函数
  function handleEdit(record: EntrustDisposeRecord) {
    // 跳转到增值委托页面，携带id和服务类型参数
    const id = record.hgyEntrustOrder.id;
    const serviceType = 2; // 从委托处置页面跳转，服务类型为2

    router.push({
      path: '/entrust/appreciationEntrust',
      query: {
        id: id,
        serviceType: serviceType,
      },
    });
  }

  function handleViewDetail(record: EntrustDisposeRecord) {
    // 转换数据格式为 AuditRecord
    const auditRecord: AuditRecord = {
      id: record.hgyEntrustOrder.id,
      entrustType: 1, // 增值委托
      serviceType: 2, // 资产处置
      status: record.hgyAssetEntrust.status || 2,
      projectName: record.hgyAssetEntrust.assetName || '-',
      relationUser: record.hgyEntrustOrder.relationUser || '-',
      relationPhone: record.hgyEntrustOrder.relationPhone || '-',
      applicantUser: record.hgyEntrustOrder.entrustCompanyName || '-',
      auditUser: '-',
      submitTime: record.hgyAssetEntrust.createTime || '-',
      auditTime: '-',
    };

    currentRecord.value = auditRecord;
    detailVisible.value = true;
  }

  // 关闭详情弹窗
  function handleDetailClose() {
    detailVisible.value = false;
    currentRecord.value = null;
  }

  function handleWithdraw(record: EntrustDisposeRecord) {
    createMessage.info('撤拍功能开发中...');
    console.log('撤拍记录:', record);
  }

  async function handleDelete(record: EntrustDisposeRecord) {
    try {
      await customEntrustDelete(record.hgyEntrustOrder.id);
      createMessage.success('删除成功');
      // 刷新表格数据
      reload();
    } catch (error) {
      console.error('删除失败:', error);
      createMessage.error('删除失败');
    }
  }

  function handleRegistrationManage(record: EntrustDisposeRecord) {
    createMessage.info('报名管理功能开发中...');
    console.log('报名管理:', record);
  }

  function handleCompanyDetail(record: EntrustDisposeRecord) {
    createMessage.info('数企详情功能开发中...');
    console.log('数企详情:', record);
  }

  function handleBiddingRecord(record: EntrustDisposeRecord) {
    createMessage.info('竞价记录功能开发中...');
    console.log('竞价记录:', record);
  }

  function handleSettlementInfo(record: EntrustDisposeRecord) {
    createMessage.info('结算信息功能开发中...');
    console.log('结算信息:', record);
  }

  function handleDealConfirmation(record: EntrustDisposeRecord) {
    createMessage.info('成交确认书功能开发中...');
    console.log('成交确认书:', record);
  }

  function handleBidderList(record: EntrustDisposeRecord) {
    createMessage.info('竞买人列表功能开发中...');
    console.log('竞买人列表:', record);
  }

  function handleWorkReport(record: EntrustDisposeRecord) {
    createMessage.info('工作报告书功能开发中...');
    console.log('工作报告书:', record);
  }

  // 状态处理函数
  function getStatusText(status: number) {
    const statusMap: Record<number, string> = {
      1: '草稿',
      2: '待审核',
      3: '审核通过',
      4: '审核拒绝',
      5: '已发布',
      6: '已成交',
      7: '已撤拍',
    };
    return statusMap[status] || '未知';
  }

  function getStatusColor(status: number) {
    console.log('status', status);
    const colorMap: Record<number, string> = {
      1: 'default',
      2: 'processing',
      3: 'success',
      4: 'error',
      5: 'cyan',
      6: 'green',
      7: 'red',
    };
    return colorMap[status] || 'default';
  }

  // 金额格式化函数
  function formatAmount(amount: number | string | null | undefined) {
    if (!amount && amount !== 0) return '-';
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(Number(amount));
  }
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
    :deep(.ant-pagination) {
      margin-bottom: -24px !important;
    }
    :deep(.ant-form) {
      padding: 0;
    }
  }

  :deep(.ant-form-item-control-input-content) {
    /* display: flex;
    justify-content: end; */
    button {
      margin-right: 0;
      margin-left: 8px;
      box-shadow: 0 0 0 rgba(3, 38, 43, 0.42);
    }
  }
</style>
