<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              label: '重发',
              icon: 'ant-design:redo-outlined',
              onClick: handleResend.bind(null, record),
              ifShow: () => record.sendStatus === 'failed',
            },
            {
              label: '删除',
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '确定要删除这条留言吗？',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
      <template #sendStatus="{ record }">
        <a-tag :color="getStatusColor(record.sendStatus)">
          {{ getStatusText(record.sendStatus) }}
        </a-tag>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { FormSchema } from '/@/components/Form';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Tag as ATag } from 'ant-design-vue';

  const { createMessage } = useMessage();

  // 表格列配置
  const columns: BasicColumn[] = [
    {
      title: '接收者',
      dataIndex: 'receiver',
      width: 120,
      resizable: true,
    },
    {
      title: '关联产品信息',
      dataIndex: 'productInfo',
      width: 200,
      resizable: true,
    },
    {
      title: '留言内容',
      dataIndex: 'content',
      width: 300,
      resizable: true,
      ellipsis: true,
    },
    {
      title: '发送状态',
      dataIndex: 'sendStatus',
      width: 120,
      resizable: true,
      slots: { customRender: 'sendStatus' },
    },
    {
      title: '发送时间',
      dataIndex: 'sendTime',
      width: 180,
      resizable: true,
    },
  ];

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'sendStatus',
      label: '发送状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择发送状态',
        options: [
          { label: '发送成功', value: 'success' },
          { label: '发送失败', value: 'failed' },
          { label: '发送中', value: 'sending' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'messageStatus',
      label: '消息状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择消息状态',
        options: [
          { label: '未读', value: 'unread' },
          { label: '已读', value: 'read' },
          { label: '已回复', value: 'replied' },
        ],
      },
      colProps: { span: 6 },
    },
    {
      field: 'timeRange',
      label: '时间区间',
      component: 'RangePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: ['开始时间', '结束时间'],
      },
      colProps: { span: 8 },
    },
  ];

  // 模拟数据
  const mockData = [
    {
      id: '1',
      receiver: '客服小王',
      productInfo: '智能手机 iPhone 15',
      content: '您好，我想了解一下这款手机的详细配置和价格信息。',
      sendStatus: 'success',
      messageStatus: 'read',
      sendTime: '2024-01-15 10:30:00',
    },
    {
      id: '2',
      receiver: '技术支持',
      productInfo: '笔记本电脑 MacBook Pro',
      content: '电脑出现了一些问题，希望能得到技术支持。',
      sendStatus: 'failed',
      messageStatus: 'unread',
      sendTime: '2024-01-15 14:20:00',
    },
    {
      id: '3',
      receiver: '销售顾问',
      productInfo: '无线耳机 AirPods Pro',
      content: '想要购买这款耳机，请问有什么优惠活动吗？',
      sendStatus: 'success',
      messageStatus: 'replied',
      sendTime: '2024-01-16 09:15:00',
    },
    {
      id: '4',
      receiver: '客服小李',
      productInfo: '智能手表 Apple Watch',
      content: '手表的保修政策是怎样的？',
      sendStatus: 'sending',
      messageStatus: 'unread',
      sendTime: '2024-01-16 16:45:00',
    },
    {
      id: '5',
      receiver: '产品专员',
      productInfo: '平板电脑 iPad Air',
      content: '请问这款平板适合学生使用吗？有教育优惠吗？',
      sendStatus: 'success',
      messageStatus: 'read',
      sendTime: '2024-01-17 11:30:00',
    },
  ];

  // 获取状态颜色
  function getStatusColor(status: string) {
    const colorMap = {
      success: 'green',
      failed: 'red',
      sending: 'blue',
    };
    return colorMap[status] || 'default';
  }

  // 获取状态文本
  function getStatusText(status: string) {
    const textMap = {
      success: '发送成功',
      failed: '发送失败',
      sending: '发送中',
    };
    return textMap[status] || status;
  }

  // 模拟API请求
  const fetchData = async (params: any) => {
    console.log('查询参数:', params);
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    return {
      items: mockData,
      total: mockData.length,
    };
  };

  // 表格配置
  const [registerTable, { reload }] = useTable({
    title: '我发出的留言',
    api: fetchData,
    columns,
    formConfig: {
      labelWidth: 80,
      schemas: searchFormSchema,
    },
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: false,
    canResize: true,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    rowKey: 'id',
  });

  // 重发留言
  function handleResend(record: any) {
    console.log('重发留言:', record);
    createMessage.info(`正在重发给 ${record.receiver} 的留言...`);
    // 模拟重发操作
    setTimeout(() => {
      createMessage.success('留言重发成功');
      reload();
    }, 1000);
  }

  // 删除留言
  function handleDelete(record: any) {
    console.log('删除留言:', record);
    createMessage.success(`已删除发给 ${record.receiver} 的留言`);
    reload();
  }
</script>

<style scoped></style>
