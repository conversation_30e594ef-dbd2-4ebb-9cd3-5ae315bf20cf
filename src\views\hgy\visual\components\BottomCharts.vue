<template>
  <div class="bottom-charts">
    <!-- 成交额排名 -->
    <div class="chart-card ranking-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-title">成交额排名</div>
        <div class="ranking-grid">
          <!-- 前三名 -->
          <div class="top-row">
            <div v-for="(item, index) in topThreeRanking" :key="index" class="ranking-medal" :class="`rank-${index + 1}`">
              <div class="medal-bg-container"></div>
              <div class="medal-content">
                <div class="medal-icon">
                  <div class="medal-bg"></div>
                  <span class="rank-number">{{ index + 1 }}</span>
                </div>
                <div class="medal-info">
                  <div class="medal-value">
                    {{ item.value }}
                    <span class="medal-unit">万</span>
                  </div>
                  <div class="medal-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 后三名 -->
          <div class="bottom-row">
            <div v-for="(item, index) in bottomThreeRanking" :key="index" class="ranking-medal" :class="`rank-${index + 4}`">
              <div class="medal-bg-container"></div>
              <div class="medal-content">
                <div class="medal-icon">
                  <div class="medal-bg"></div>
                  <span class="rank-number">{{ index + 4 }}</span>
                </div>
                <div class="medal-info">
                  <div class="medal-value">{{ item.value }} 万</div>
                  <div class="medal-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 标的溢价趋势 -->
    <div class="chart-card trend-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-header">
          <div class="card-title">标的溢价趋势</div>
          <div class="card-controls">
            <div class="chart-legend">
              <div class="legend-item">
                <div class="legend-icon line-icon"></div>
                <span class="legend-text">溢价率</span>
              </div>
              <div class="legend-item">
                <div class="legend-icon bar-icon"></div>
                <span class="legend-text">溢价额</span>
              </div>
            </div>
            <div class="chart-filter">
              <select class="filter-select">
                <option value="30">近30天</option>
                <option value="60">近60天</option>
                <option value="90">近90天</option>
              </select>
            </div>
          </div>
        </div>
        <div class="chart-wrapper">
          <div ref="trendChartRef" class="chart"></div>
        </div>
      </div>
    </div>

    <!-- 资产处置溢价趋势 -->
    <div class="chart-card asset-trend-card">
      <div class="card-bg"></div>
      <div class="card-content">
        <div class="card-header">
          <div class="card-title">资产处置溢价趋势</div>
          <div class="card-controls">
            <div class="chart-legend">
              <div class="legend-item">
                <div class="legend-icon line-icon"></div>
                <span class="legend-text">溢价率</span>
              </div>
              <div class="legend-item">
                <div class="legend-icon bar-icon"></div>
                <span class="legend-text">溢价额</span>
              </div>
            </div>
            <div class="chart-filter">
              <select class="filter-select">
                <option value="30">近30天</option>
                <option value="60">近60天</option>
                <option value="90">近90天</option>
              </select>
            </div>
          </div>
        </div>
        <div class="chart-wrapper">
          <div ref="assetTrendChartRef" class="chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick } from 'vue';
  import * as echarts from 'echarts';

  const trendChartRef = ref<HTMLElement>();
  const assetTrendChartRef = ref<HTMLElement>();
  let trendChart: echarts.ECharts | null = null;
  let assetTrendChart: echarts.ECharts | null = null;

  // 排名数据
  const topThreeRanking = ref([
    { name: '标的某某某项目A', value: 969.26 },
    { name: '标的某某某项目B', value: 969.26 },
    { name: '标的某某某项目C', value: 969.26 },
  ]);

  const bottomThreeRanking = ref([
    { name: '标的某某某项目D', value: 969.26 },
    { name: '标的某某某项目E', value: 969.26 },
    { name: '标的某某某项目F', value: 969.26 },
  ]);

  // 初始化趋势图表
  const initTrendChart = () => {
    if (!trendChartRef.value) return;

    trendChart = echarts.init(trendChartRef.value);

    const option = {
      backgroundColor: 'transparent',
      grid: {
        top: '18%',
        left: '2px',
        right: '2px',
        bottom: '2px',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['6.28', '7.01', '7.04', '7.07', '7.10', '7.13', '7.16', '7.19', '7.22', '7.25'],
        axisLine: {
          lineStyle: {
            color: '#2BCCFF',
            width: 1,
          },
        },
        axisLabel: {
          color: '#2BCCFF',
          fontSize: 12,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '万元',
          nameTextStyle: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed',
            },
          },
        },
        {
          type: 'value',
          name: '%',
          nameTextStyle: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#2BCCFF',
            fontSize: 12,
          },
        },
      ],
      series: [
        {
          name: '万元',
          type: 'bar',
          yAxisIndex: 0,
          barWidth: 20,
          z: 2,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2BCCFF' },
              { offset: 1, color: '#044D60' },
            ]),
            borderRadius: [4, 4, 0, 0],
          },
          data: [300, 500, 200, 600, 400, 700, 350, 200, 650, 600],
        },
        {
          name: '%',
          type: 'line',
          yAxisIndex: 1,
          smooth: false,
          symbol: 'circle',
          symbolSize: [8, 8],
          z: 1,
          lineStyle: {
            width: 2,
            color: '#2BCCFF',
          },
          itemStyle: {
            color: '#ffffff',
            borderColor: '#2BCCFF',
            borderWidth: 3,
          },
          areaStyle: {
            color: '#2BCCFF',
            opacity: 0.3,
          },
          data: [40, 80, 60, 90, 70, 50, 80, 30, 100, 95],
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#2BCCFF',
        borderWidth: 1,
        textStyle: {
          color: '#2BCCFF',
          fontSize: 12,
        },
      },
    };

    trendChart.setOption(option);
  };

  // 初始化资产处置趋势图表
  const initAssetTrendChart = () => {
    if (!assetTrendChartRef.value) return;

    assetTrendChart = echarts.init(assetTrendChartRef.value);

    // 使用相同的配置，但数据略有不同
    const option = {
      backgroundColor: 'transparent',
      grid: {
        top: '18%',
        left: '2px',
        right: '2px',
        bottom: '2px',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['6.28', '7.01', '7.04', '7.07', '7.10', '7.13', '7.16', '7.19', '7.22', '7.25'],
        axisLine: {
          lineStyle: {
            color: '#2BCCFF',
            width: 1,
          },
        },
        axisLabel: {
          color: '#2BCCFF',
          fontSize: 12,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '万元',
          nameTextStyle: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed',
            },
          },
        },
        {
          type: 'value',
          name: '%',
          nameTextStyle: {
            color: '#2BCCFF',
            fontSize: 12,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#2BCCFF',
            fontSize: 12,
          },
        },
      ],
      series: [
        {
          name: '万元',
          type: 'bar',
          yAxisIndex: 0,
          barWidth: 20,
          z: 2,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2BCCFF' },
              { offset: 1, color: '#044D60' },
            ]),
            borderRadius: [4, 4, 0, 0],
          },
          data: [250, 450, 180, 550, 380, 650, 320, 180, 600, 550],
        },
        {
          name: '%',
          type: 'line',
          yAxisIndex: 1,
          smooth: false,
          symbol: 'circle',
          symbolSize: [8, 8],
          z: 1,
          lineStyle: {
            width: 2,
            color: '#2BCCFF',
          },
          itemStyle: {
            color: '#ffffff',
            borderColor: '#2BCCFF',
            borderWidth: 3,
          },
          areaStyle: {
            color: '#2BCCFF',
            opacity: 0.3,
          },
          data: [35, 75, 55, 85, 65, 45, 75, 25, 95, 90],
        },
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#2BCCFF',
        borderWidth: 1,
        textStyle: {
          color: '#2BCCFF',
          fontSize: 12,
        },
      },
    };

    assetTrendChart.setOption(option);
  };

  // 窗口大小变化时重新调整图表
  const handleResize = () => {
    trendChart?.resize();
    assetTrendChart?.resize();
  };

  onMounted(async () => {
    await nextTick();
    initTrendChart();
    initAssetTrendChart();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    trendChart?.dispose();
    assetTrendChart?.dispose();
    window.removeEventListener('resize', handleResize);
  });
</script>

<style lang="less" scoped>
  .bottom-charts {
    display: flex;
    height: 281px;
    gap: 20px;
  }

  .chart-card {
    position: relative;
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
  }

  .card-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('@/assets/visual/bottom-bg.png') no-repeat center center;
    background-size: cover;
    z-index: 1;
  }

  .card-content {
    position: relative;
    height: 100%;
    padding: 7px 20px;
    z-index: 2;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-title {
    color: #fff;
    font-size: 22px;
    font-family: 'YouSheBiaoTiHei';
  }

  .card-controls {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .chart-legend {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .legend-icon {
    flex-shrink: 0;
  }

  .line-icon {
    position: relative;
    width: 16px;
    height: 12px;
    background: transparent;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 16px;
      height: 2px;
      background: #2bccff;
      transform: translateY(-50%);
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 6px;
      height: 6px;
      background: #ffffff;
      border: 2px solid #2bccff;
      border-radius: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .bar-icon {
    width: 28px;
    height: 11px;
    background: #2bccff;
    border-radius: 2px;
  }

  .legend-text {
    color: #ffffff;
    font-size: 12px;
    font-weight: 400;
  }

  .chart-filter {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 12px;
      width: 12px;
      height: 8px;
      background: url('@/assets/visual/center/down.png') no-repeat center center;
      background-size: contain;
      transform: translateY(-50%);
      pointer-events: none;
      z-index: 1;
    }
  }

  .filter-select {
    appearance: none;
    background: url('@/assets/visual/center/select-bg-mini.png') no-repeat center center;
    background-size: 100% 100%;
    border: none;
    outline: none;
    color: #2bccff;
    font-size: 12px;
    font-weight: 400;
    padding: 8px 32px 8px 16px;
    cursor: pointer;
    min-width: 90px;
    height: 32px;
    line-height: 16px;
    text-align: left;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
    }

    &:focus {
      box-shadow: 0 0 8px rgba(43, 204, 255, 0.3);
    }

    // 优化选项样式
    option {
      background: #0a4a52;
      color: #2bccff;
      padding: 8px 16px;
      border: none;
      font-size: 12px;
      font-weight: 400;

      &:hover {
        background: #0d5a64;
      }

      &:checked {
        background: #2bccff;
        color: #ffffff;
      }
    }
  }

  .ranking-grid {
    height: calc(100% - 40px);
    display: flex;
    flex-direction: column;
  }

  .top-row,
  .bottom-row {
    display: flex;
    justify-content: space-around;
    flex: 1;
  }

  .ranking-medal {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 161px;
    height: 115px;
  }

  .medal-bg-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('@/assets/visual/center/cj-bg.png') no-repeat center center;
    background-size: contain;
    z-index: 1;
  }

  .medal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    height: 100%;
    justify-content: center;
    gap: 6px;
  }

  .medal-icon {
    position: relative;
    width: 57.6px;
    height: 36px;
    flex-shrink: 0;
    margin-top: 13px;

    .medal-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      background-image: url('@/assets/visual/medal-regular.png');
    }

    .rank-number {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #004c66 !important;
      font-size: 11px;
      z-index: 2;
      font-family: 'DIN Bold';
    }
  }

  .ranking-medal.rank-1 .medal-icon .medal-bg {
    background-image: url('@/assets/visual/medal-gold.png');
  }

  .ranking-medal.rank-2 .medal-icon .medal-bg {
    background-image: url('@/assets/visual/medal-silver.png');
  }

  .ranking-medal.rank-3 .medal-icon .medal-bg {
    background-image: url('@/assets/visual/medal-cuprum.png');
  }

  .medal-info {
    text-align: center;
  }

  .medal-value {
    color: #fff;
    font-size: 22px;
    text-shadow: 0 0 8px rgba(18, 230, 219, 0.5);
    font-family: 'DIN Regular';
    .medal-unit {
      font-size: 14px;
      font-family: 'PingFang Medium';
    }
  }

  .medal-name {
    color: #ffffff;
    font-size: 14px;
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 110px;
  }

  .chart-wrapper {
    height: calc(100% - 40px);
  }

  .chart {
    width: 100%;
    height: 100%;
  }
</style>
